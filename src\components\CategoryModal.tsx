// 分类模态窗口组件 - 管理分类相关的模态窗口

import React from 'react'
import { X, AlertTriangle } from 'lucide-react'
import CategoryForm from './CategoryForm'
import { useModalScrollLock } from '../utils/scrollManager'
import type { Category, CategoryInput, CategoryUpdate } from '../types'

interface CategoryModalProps {
  /** 是否显示模态窗口 */
  isOpen: boolean
  /** 模态窗口类型 */
  type: 'create' | 'edit' | 'delete'
  /** 要编辑或删除的分类 */
  category?: Category
  /** 分类的书签数量（删除时显示） */
  bookmarkCount?: number
  /** 保存回调 */
  onSave?: (data: CategoryInput | CategoryUpdate) => Promise<void>
  /** 删除回调 */
  onDelete?: () => Promise<void>
  /** 关闭回调 */
  onClose: () => void
  /** 是否正在加载 */
  loading?: boolean
}

/**
 * 分类模态窗口组件
 * 支持创建、编辑、删除确认三种类型的模态窗口
 */
const CategoryModal: React.FC<CategoryModalProps> = React.memo(({
  isOpen,
  type,
  category,
  bookmarkCount = 0,
  onSave,
  onDelete,
  onClose,
  loading = false
}) => {
  // 使用统一的滚动锁定管理
  useModalScrollLock(isOpen, 'CategoryModal')

  // 处理ESC键关闭
  React.useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      // 只有在模态窗口打开且未加载时才处理ESC键
      if (isOpen && e.key === 'Escape' && !loading) {
        onClose()
      }
    }

    // 始终添加事件监听器，但在处理器中检查isOpen状态
    document.addEventListener('keydown', handleEscKey)

    return () => {
      document.removeEventListener('keydown', handleEscKey)
    }
  }, [isOpen, loading, onClose])

  // 如果模态窗口未打开，不渲染
  if (!isOpen) {
    return null
  }

  // 处理背景点击关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !loading) {
      onClose()
    }
  }

  // 获取模态窗口标题
  const getModalTitle = () => {
    switch (type) {
      case 'create':
        return '创建新分类'
      case 'edit':
        return '编辑分类'
      case 'delete':
        return '删除分类'
      default:
        return '分类管理'
    }
  }

  // 处理删除确认
  const handleDeleteConfirm = async () => {
    if (onDelete) {
      await onDelete()
    }
  }

  // 渲染删除确认内容
  const renderDeleteContent = () => {
    if (!category) return null

    return (
      <div className="space-y-4">
        {/* 警告图标和标题 */}
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              确认删除分类
            </h3>
            <p className="text-sm text-gray-500">
              此操作无法撤销
            </p>
          </div>
        </div>

        {/* 分类信息 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: category.color || '#6B7280' }}
            />
            <div>
              <p className="font-medium text-gray-900">{category.name}</p>
              {category.description && (
                <p className="text-sm text-gray-600">{category.description}</p>
              )}
            </div>
          </div>
        </div>

        {/* 影响说明 */}
        <div className="space-y-2">
          <p className="text-sm text-gray-700">
            删除此分类将会：
          </p>
          <ul className="text-sm text-gray-600 space-y-1 ml-4">
            <li>• 永久删除分类 "{category.name}"</li>
            {bookmarkCount > 0 ? (
              <li>• 将 {bookmarkCount} 个书签移动到"默认分类"</li>
            ) : (
              <li>• 不会影响任何书签（此分类为空）</li>
            )}
            <li>• 此操作无法撤销</li>
          </ul>
        </div>

        {/* 确认按钮 */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
            disabled={loading}
          >
            取消
          </button>
          <button
            type="button"
            onClick={handleDeleteConfirm}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                删除中...
              </div>
            ) : (
              '确认删除'
            )}
          </button>
        </div>
      </div>
    )
  }

  // 渲染表单内容
  const renderFormContent = () => {
    if (type === 'delete') {
      return renderDeleteContent()
    }

    if (!onSave) {
      console.error('CategoryModal: onSave is required for create/edit mode')
      return null
    }

    return (
      <CategoryForm
        category={category}
        onSubmit={onSave}
        onCancel={onClose}
        loading={loading}
        mode={type}
      />
    )
  }

  return (
    <div 
      className="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      {/* 背景遮罩 */}
      <div 
        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        onClick={handleBackdropClick}
      >
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          aria-hidden="true"
        />

        {/* 垂直居中的技巧 */}
        <span 
          className="hidden sm:inline-block sm:align-middle sm:h-screen" 
          aria-hidden="true"
        >
          &#8203;
        </span>

        {/* 模态窗口内容 */}
        <div className="relative inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          {/* 头部 */}
          <div className="flex items-center justify-between mb-4">
            <h3 
              className="text-lg font-medium text-gray-900"
              id="modal-title"
            >
              {getModalTitle()}
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-1 transition-colors"
              disabled={loading}
              aria-label="关闭"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* 内容区域 */}
          <div>
            {renderFormContent()}
          </div>
        </div>
      </div>
    </div>
  )
})

// 设置显示名称便于调试
CategoryModal.displayName = 'CategoryModal'

export default CategoryModal

// 导出类型定义
export type { CategoryModalProps }