/**
 * React错误#310修复验证测试
 * 测试CategoryModal和TagModal组件的hooks规则合规性
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import CategoryModal from '../src/components/CategoryModal'
import TagModal from '../src/components/TagModal'
import type { Category, Tag } from '../src/types'

// Mock scrollManager
vi.mock('../src/utils/scrollManager', () => ({
  useModalScrollLock: vi.fn(),
  ScrollLockReason: {
    MODAL: 'modal'
  }
}))

describe('React错误#310修复验证', () => {
  const mockCategory: Category = {
    id: '1',
    name: '测试分类',
    description: '测试描述',
    color: '#3B82F6',
    createdAt: new Date(),
    updatedAt: new Date(),
    bookmarkCount: 5
  }

  const mockTag: Tag = {
    id: '1',
    name: '测试标签',
    color: '#3B82F6',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 3
  }

  describe('CategoryModal hooks规则合规性', () => {
    it('应该在isOpen=false时正确渲染而不违反hooks规则', () => {
      const mockOnClose = vi.fn()
      const mockOnSave = vi.fn()

      // 这个测试确保即使isOpen=false，hooks也会被正确调用
      const { rerender } = render(
        <CategoryModal
          isOpen={false}
          type="edit"
          category={mockCategory}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 组件应该返回null但不应该抛出hooks错误
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()

      // 重新渲染为打开状态应该正常工作
      rerender(
        <CategoryModal
          isOpen={true}
          type="edit"
          category={mockCategory}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })

    it('应该在多次开关状态时保持hooks调用顺序一致', () => {
      const mockOnClose = vi.fn()
      const mockOnSave = vi.fn()

      const { rerender } = render(
        <CategoryModal
          isOpen={false}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 多次切换状态，确保hooks调用顺序一致
      for (let i = 0; i < 5; i++) {
        rerender(
          <CategoryModal
            isOpen={true}
            type="create"
            onSave={mockOnSave}
            onClose={mockOnClose}
          />
        )

        rerender(
          <CategoryModal
            isOpen={false}
            type="create"
            onSave={mockOnSave}
            onClose={mockOnClose}
          />
        )
      }

      // 如果hooks规则被违反，这里会抛出错误
      expect(true).toBe(true) // 测试通过表示没有hooks错误
    })

    it('应该正确处理ESC键事件', () => {
      const mockOnClose = vi.fn()
      const mockOnSave = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="edit"
          category={mockCategory}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 模拟ESC键按下
      fireEvent.keyDown(document, { key: 'Escape' })

      expect(mockOnClose).toHaveBeenCalledTimes(1)
    })
  })

  describe('TagModal hooks规则合规性', () => {
    it('应该在isOpen=false时正确渲染而不违反hooks规则', () => {
      const mockOnClose = vi.fn()
      const mockOnSave = vi.fn()

      const { rerender } = render(
        <TagModal
          isOpen={false}
          type="edit"
          tag={mockTag}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()

      rerender(
        <TagModal
          isOpen={true}
          type="edit"
          tag={mockTag}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })

    it('应该在多次开关状态时保持hooks调用顺序一致', () => {
      const mockOnClose = vi.fn()
      const mockOnSave = vi.fn()

      const { rerender } = render(
        <TagModal
          isOpen={false}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      for (let i = 0; i < 5; i++) {
        rerender(
          <TagModal
            isOpen={true}
            type="create"
            onSave={mockOnSave}
            onClose={mockOnClose}
          />
        )

        rerender(
          <TagModal
            isOpen={false}
            type="create"
            onSave={mockOnSave}
            onClose={mockOnClose}
          />
        )
      }

      expect(true).toBe(true)
    })

    it('应该正确处理ESC键事件', () => {
      const mockOnClose = vi.fn()
      const mockOnSave = vi.fn()

      render(
        <TagModal
          isOpen={true}
          type="edit"
          tag={mockTag}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      fireEvent.keyDown(document, { key: 'Escape' })

      expect(mockOnClose).toHaveBeenCalledTimes(1)
    })
  })

  describe('hooks调用顺序验证', () => {
    it('CategoryModal和TagModal应该在相同条件下调用相同数量的hooks', () => {
      const mockOnClose = vi.fn()
      const mockOnSave = vi.fn()

      // 这个测试确保两个组件的hooks调用模式一致
      const categoryResult = render(
        <CategoryModal
          isOpen={false}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      const tagResult = render(
        <TagModal
          isOpen={false}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 两个组件都应该能正常渲染而不抛出hooks错误
      expect(categoryResult.container).toBeDefined()
      expect(tagResult.container).toBeDefined()

      categoryResult.unmount()
      tagResult.unmount()
    })
  })
})
